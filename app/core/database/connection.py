"""
Database configuration for GrowthHive API
Single source of truth for database configuration
"""

from typing import AsyncGenerator
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.pool import NullPool
from app.core.config.settings import settings
from app.core.logging import logger

# Get database URL from settings
DATABASE_URL = settings.DATABASE_URL

# Create async engine with proper configuration and connection parameters
try:
    # Configure connection arguments for better reliability
    connect_args = {
        "command_timeout": settings.DB_COMMAND_TIMEOUT,
        "server_settings": {
            "application_name": "growthhive_api",
            "jit": "off"  # Disable JIT for better compatibility
        }
    }

    # Add SSL configuration for AWS RDS
    if "rds.amazonaws.com" in DATABASE_URL:
        connect_args.update({
            "ssl": "prefer",  # Use SSL if available, but don't require it
            "sslmode": "prefer"
        })
        logger.info("🔒 SSL configuration enabled for AWS RDS (prefer mode)")

    async_engine = create_async_engine(
        DATABASE_URL,
        echo=settings.DB_ECHO,
        poolclass=NullPool,  # Use NullPool to avoid connection pooling issues
        pool_pre_ping=True,  # Verify connections before use
        connect_args=connect_args
    )
    logger.info("✅ SQLAlchemy async engine created successfully")
except Exception as e:
    logger.error(f"⚠️ SQLAlchemy async engine creation failed: {e}")
    async_engine = None

# Session maker
AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
) if async_engine else None

# Base class for models
Base = declarative_base()

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session with improved timeout handling"""
    if not AsyncSessionLocal:
        raise RuntimeError("Database engine not initialized")

    try:
        # Use asyncio timeout for database operations
        async with AsyncSessionLocal() as session:
            try:
                yield session
            finally:
                await session.close()
    except Exception as e:
        logger.error(f"⚠️ Database session error: {e}")
        # Re-raise to let FastAPI handle the error
        raise

# --- Model imports moved to avoid circular imports ---
# Models are imported in alembic/env.py for migration discovery
# ---------------------------------------------------------------
